import { ERole } from '@/shared/types/enums'
import { IDepartment } from 'entities/department'

export interface ICheckPasswordResponse {
  error: string
  ok: boolean
}

export interface IUpdatePasswordResponse {
  token: string
}

export interface IUpdatePasswordRequest {
  password: string
  new_password: string
}

export interface ILogin {
  email: string
  password: string
}

export interface ILoginLDAP {
  username: string
  password: string
}

export interface IPasswordRecovery {
  email: string
}

export interface IPasswordRestore {
  reset_token: string
  password: string
}

export interface ILoginResponse {
  access_token: string
  token_type: 'bearer'
}

export interface IPasswordRestoreResponse {
  token: string
}

export interface IRegistration {
  first_name: string
  middle_name: string
  last_name: string
  password: string
  position: string
  department: {
    id: UUID
    title: string
  }
  invite_token: string
  agree: boolean
}

export interface IRegistrationResponse {
  auth_token: string
  refresh_token: string
  token_type: 'bearer'
}

export interface IRegistrationInfo {
  invite_token: string
}

export interface IRegistrationInfoResponse {
  all_departments: IDepartment[]
}

export interface AuthApiError {
  data: {
    message: string
    value?: string // qr-код для двухфакторки
  }
  status: number
}

export interface IPasswordResetTokenInfo {
  user_id: UUID
  roles: ERole[]
  email: string
  organization: {
    id: UUID
    title: string
  }
}
