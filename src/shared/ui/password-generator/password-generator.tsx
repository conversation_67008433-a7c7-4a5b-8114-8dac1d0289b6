import { FC, useState } from 'react'
import { Input, Button } from '@/shared/ui'
import { authAPI } from 'entities/auth'
import { useTranslation } from 'react-i18next'
import classNamesBind from 'classnames/bind'
import styles from './password-generator.module.scss'
import { useCopyToClipboard } from '@/shared/hooks'
import { ERole } from '@/shared/types/enums'

const cx = classNamesBind.bind(styles)

interface PasswordGeneratorProps {
  organizationId?: string
  roles?: ERole[] | null | undefined
  onPasswordChange: (password: string) => void
  className?: string
}

export const PasswordGenerator: FC<PasswordGeneratorProps> = ({
  organizationId,
  roles,
  onPasswordChange,
  className,
}) => {
  const { t } = useTranslation()
  const [generatedPassword, setGeneratedPassword] = useState('')
  const [generatePassword, { isLoading }] = authAPI.useGeneratePasswordMutation()
  const { copyToClipboard } = useCopyToClipboard({ copyValue: generatedPassword })

  const handleGenerate = async () => {
    try {
      if (!organizationId) return

      const resultRole =
        roles && roles.length > 0
          ? roles.includes(ERole.operator)
            ? ERole.operator
            : ERole.employee
          : ERole.employee

      const result = await generatePassword({
        organization_id: organizationId,
        role: resultRole,
      }).unwrap()

      setGeneratedPassword(result.password)
      onPasswordChange(result.password)
    } catch (error) {
      console.error('Failed to generate password:', error)
    }
  }

  return (
    <div className={cx('wrapper', className)}>
      <h3 className={cx('title')}>{t('commons:generator')}</h3>
      <Input
        placeholder={t('commons:generated_password')}
        value={generatedPassword}
        readOnly
        classNameWrapper={cx('input')}
        fullWidth
      />
      <div className={cx('buttons')}>
        <Button
          color='darkGray'
          onClick={handleGenerate}
          disabled={isLoading}
          loading={isLoading}
          className={cx('btn')}
        >
          {t('commons:generate')}
        </Button>
        <Button
          color='gray'
          onClick={copyToClipboard}
          disabled={isLoading || !generatedPassword}
          className={cx('btn')}
        >
          {t('commons:copy')}
        </Button>
      </div>
    </div>
  )
}
