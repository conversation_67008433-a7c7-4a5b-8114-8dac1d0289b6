/* eslint-disable @typescript-eslint/no-explicit-any */
import { useMemo, useState, useEffect } from 'react'
import {
  useGetMyCoursesQuery,
  useGetMyScormCoursesQuery,
} from '@/store/services/user-courses-service'
import { getCompletedCourses } from '@/shared/helpers/courses'
import styles from './certificates-modal.module.scss'
import classNamesBind from 'classnames/bind'
import noCoursesIcon from './noCoursesIcon.png'
import ChevroneSmallIcon from '@/shared/ui/Icon/icons/components/ChevroneSmallIcon'
import { IconWrapper } from '@/shared/ui/Icon/IconWrapper'
import { useTranslation } from 'react-i18next'
import { myCoursesQueryApi } from '../../../entities/courses'
import { sharedCoursesApi } from '../../../entities/courses/model/api/shared'
import { TReportType } from '../report-modal'

const cx = classNamesBind.bind(styles)

export const useCertificatesModal = () => {
  const { t } = useTranslation('modals__certificates-modal')

  const [tab, setTab] = useState<{ label: string; value: string } | null>(null)

  const [skipCourses, setSkipCourses] = useState(true)
  const [skipScormCourses, setSkipScormCourses] = useState(true)

  useEffect(() => {
    if (!tab) return
    setSkipCourses(tab.value !== 'archive')
    setSkipScormCourses(tab.value !== 'archive')
  }, [tab])

  const {
    data: courses,
    isLoading: isCoursesLoading,
    isError: isCoursesError,
  } = useGetMyCoursesQuery(undefined, { skip: skipCourses })

  const {
    data: scromCourses,
    isLoading: isScormCoursesLoading,
    isError: isScormCoursesError,
  } = useGetMyScormCoursesQuery(undefined, { skip: skipScormCourses })

  const { data: userCourse } = myCoursesQueryApi.useGetMyCoursesQuery({
    limit: Number.MAX_SAFE_INTEGER,
  })

  const { data: userSharedCourses } = sharedCoursesApi.useGetMySharedCoursesQuery({
    limit: Number.MAX_SAFE_INTEGER,
    status: 'completed',
  })

  const isLoading = isScormCoursesLoading || isCoursesLoading
  const isError = isScormCoursesError || isCoursesError

  const userCompletedCourses = useMemo(() => {
    const completedRegularCourses = userCourse?.data.filter(c => c.statistics?.overall >= 100) || []

    const completedSharedCourses =
      userSharedCourses?.data.filter(c => c.statistics?.overall >= 100) || []

    return [...completedRegularCourses, ...completedSharedCourses]
  }, [userCourse, userSharedCourses])

  const allArchiveCourses = useMemo(
    () => [...(courses?.data || []), ...(scromCourses?.data || [])],
    [courses, scromCourses],
  ) as any

  const completedCourses = useMemo(
    () => getCompletedCourses(allArchiveCourses),
    [allArchiveCourses],
  )

  const TABS_CONFIG = useMemo(
    () => [
      {
        label: t('title'),
        value: 'active',
        content: (
          <>
            {userCompletedCourses && (
              <>
                {!userCompletedCourses?.length && (
                  <div className={cx('empty')}>
                    <img src={noCoursesIcon} alt='' />
                    <div className={cx('empty-title')}>{t('no_finished_courses')}</div>
                  </div>
                )}
                {!!userCompletedCourses?.length && (
                  <div className={cx('inner')}>
                    {userCompletedCourses?.map((course, i) => {
                      const с = course?.assigned_course

                      return (
                        <div
                          key={`my-course-certificate-${с.id}`}
                          className={cx('item')}
                          onClick={() => onClick(с.id, 'courseV2')}
                        >
                          <span>{i + 1}.</span>
                          <div className={cx('itemTitle')}>{с.title}</div>
                          <IconWrapper>
                            <ChevroneSmallIcon />
                          </IconWrapper>
                        </div>
                      )
                    })}
                  </div>
                )}
              </>
            )}
          </>
        ),
      },
    ],
    [t, userCompletedCourses],
  )

  useEffect(() => {
    if (!tab) {
      setTab(TABS_CONFIG[0])
    }
  }, [TABS_CONFIG, tab])

  const [openSuccessModal, setOpenSuccesModal] = useState<{
    id: UUID
    type: 'scorm' | 'course' | 'courseV2'
  } | null>(null)

  const onClick = async (id: UUID, type: 'scorm' | 'course' | 'courseV2') => {
    setOpenSuccesModal({ id, type })
  }

  const getReportModalType = (type: 'scorm' | 'course' | 'courseV2'): TReportType => {
    if (type === 'scorm') return 'certificateScorm'
    if (type === 'course') return 'certificate'
    return 'certificateCourseV2'
  }

  return {
    isLoading,
    isError,
    completedCourses,
    openSuccessModal,
    setOpenSuccesModal,
    onClick,
    TABS_CONFIG,
    tab,
    setTab,
    getReportModalType,
  }
}
