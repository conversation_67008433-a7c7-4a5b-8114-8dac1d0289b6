/* eslint-disable @typescript-eslint/ban-ts-comment */
/* eslint-disable @typescript-eslint/no-explicit-any */
import { FC, useCallback, useEffect, useMemo, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { SubmitHand<PERSON>, useForm } from 'react-hook-form'
import classNamesBind from 'classnames/bind'
import { v4 as uuid } from 'uuid'
import styles from './create-campaign.module.scss'
import { CreateCampaignProps } from './create-campaign.d'
import { SelectCourseCard, SelectTargetCard, SelectTemplateCard } from '@/shared/components'
import {
  PageTitle,
  Breadcrumbs,
  Button,
  HelpIcon,
  ITabSelector,
  Input,
  Switch,
  TabSelector,
  Textarea,
  IListItem,
  MultiSelect,
} from '@/shared/ui'
import { TDateType, TEmailSendEndType, TTargetAutoCourseType, TTargetType } from './config'
import { ITargetData } from '@/entities/target'

import { useNavigate } from 'react-router-dom'
import { handleErrorResponseAndTranslate } from '@/shared/contexts/notifications/helper'
import { ITagWithNew } from '@/shared/types/store/tag'
import { CustomTagsSelect } from '@/shared/components/custom-tags-select'
import { SelectRedirectPage } from '@/shared/components/select-redirect-page'
import { useNotification } from '@/shared/contexts/notifications'
import { useEmployees, useUserOrganizationId } from '@/entities/employee'
import { getPrettyDate } from '@/shared/helpers/date'
import { useEmployeesByTags } from '@/shared/hooks/use-employees-by-tags'
import { useLocaleForDates } from '@/shared/hooks/use-locale-for-dates'
import {
  setCreateCampaign,
  setCreateCampaignForm,
  setInitialForms,
  setPostCampaignForm,
} from '@/store/slices/phishing/phishing-slice'
import { useAppDispatch, useAppSelector } from '@/store'
import {
  selectCreateCampaign,
  CreateCampaignForm,
  selectCreateCampaignForm,
  selectCreateCampaignByEndingForm,
  selectCampaignIsAllSelected,
  selectActualCampaignSelectedUsersCount,
} from '@/store/slices/phishing'
import { useCampaign } from './use-campaign'
import { URLS } from '@/shared/configs/urls'
import { isNumber } from '@/shared/helpers'
import {
  OrganizationTree,
  OrganizationTreeProvider,
  selectLastLoadedCountOfPeople,
  useResetTreeCheck,
  useUnmountOrganizationTree,
} from '@/shared/modals/organization-tree'
import { phishingQueries, phishingMutations } from '@/entities/phishing'
import { SelectDateCardWithInput } from '@/shared/components/SelectDateCard/select-date-card-with-input'

const cx = classNamesBind.bind(styles)

export const CreateCampaign: FC<CreateCampaignProps.Props> = props => {
  const { className } = props
  const { t } = useTranslation('pages__phishing')
  const {
    isAllSelected: emplIsAllSelected,
    checkedEmployees,
    excludeEmployees,
    search,
    filter,
    useInPhishingCampaign,
    getCountOfEmployess,
    setUseInPhishingCampaign,
  } = useEmployees()
  const userOrganizationId = useUserOrganizationId()
  const dateLocale = useLocaleForDates()

  const lastLoadedCountOfPeople = useAppSelector(selectLastLoadedCountOfPeople)

  const [newCountOfEmployes, setNewCountOfEmployes] = useState<number | undefined>(
    lastLoadedCountOfPeople,
  )
  const [targetModalOpen, setTargetModalOpen] = useState(false)

  const campaign = useAppSelector(selectCreateCampaign)
  const campaignForm = useAppSelector(selectCreateCampaignForm)
  const campaignFormByEnding = useAppSelector(selectCreateCampaignByEndingForm)
  const isAllSelected = useAppSelector(selectCampaignIsAllSelected)
  const actualUsersCount = useAppSelector(selectActualCampaignSelectedUsersCount)
  const dispatch = useAppDispatch()

  const displayedUsersCount = isAllSelected
    ? actualUsersCount !== undefined
      ? actualUsersCount
      : newCountOfEmployes || 0
    : newCountOfEmployes

  const handleChangeTargetType = (item: ITabSelector) =>
    dispatch(setCreateCampaign({ targetType: item.name as TTargetType }))

  const handleChangeDateType = (item: ITabSelector) =>
    dispatch(setCreateCampaign({ dateType: item.name as TDateType }))

  const {
    register,
    handleSubmit,
    formState: { errors, isValid },
    watch,
    setError: setErrorForm,
    setValue,
  } = useForm<CreateCampaignForm>({
    mode: 'all',
    reValidateMode: 'onChange',
    defaultValues: campaignForm,
  })

  useEffect(() => {
    const subscription = watch(value => {
      const changedValue = value as CreateCampaignForm
      dispatch(setCreateCampaignForm({ ...changedValue }))
    })
    return () => subscription.unsubscribe()
  }, [dispatch, watch])

  useUnmountOrganizationTree()

  const enableRedirect = watch('enableRedirect')
  const name = watch('name')
  const isDelay = watch('isDelay')
  const isTesting = watch('isTesting')
  const hasAutoCourse = watch('hasAutoCourse')
  const targetsAutoCourse = watch('targetsAutoCourse')
  const hasMessage = watch('hasMessage')
  const targetsMessage = watch('targetsMessage')
  const course = watch('course')
  const templates = watch('templates')
  const targets = watch('targets')
  const dateEnd = watch('dateEnd')
  const dateDelay = watch('dateDelay')
  const theme = watch('theme')
  const text = watch('text')
  const excludedTags = watch('excludedTags')

  useEffect(() => {
    if (!useInPhishingCampaign) return
    dispatch(setCreateCampaign({ targetType: 'filtered' }))

    return () => setUseInPhishingCampaign(false)
  }, [useInPhishingCampaign, setUseInPhishingCampaign, dispatch])

  useEffect(() => {
    if (dateDelay && dateEnd && dateDelay > dateEnd) {
      setValue('dateEnd', dateDelay)
    }
  }, [dateDelay, setValue, dateEnd])

  useEffect(() => {
    if (!isDelay) {
      setValue('dateDelay', null)
    }
  }, [isDelay, setValue])

  const [createPhishingCampaign, { isLoading }] =
    phishingMutations.useCreatePhishingCampaignMutation()
  const [createPhishingCampaignByFilteredUsers] =
    phishingMutations.useCreatePhishingCampaignByFilteredUsersMutation()
  const [getAutoPhishFinishDate, { data: finishData, isLoading: finishDataIsLoading }] =
    phishingQueries.useLazyGetAutoPhishFinishDateQuery()

  const { DATE_EMAIL_SEND_END_TYPES, DATE_TYPES, TARGET_AUTO_COURSE_LIST, TARGET_TYPES } =
    useCampaign()

  const [error, setError] = useState('')

  const navigate = useNavigate()
  const { add } = useNotification()

  const breadcrumbItems = useMemo(
    () => [
      {
        id: '/lk/admin/phishing/campaigns',
        text: t('commons:campaigns'),
        clickable: true,
      },
      {
        id: '/lk/admin/phishing/campaigns',
        text: t('creating_new_campaign'),
        clickable: false,
      },
    ],
    [t],
  )

  const excludedIds = useMemo(() => {
    if (!excludedTags) return null

    return excludedTags.map(excludeTag => excludeTag.id)
  }, [excludedTags])

  const { usersByTags } = useEmployeesByTags(excludedIds)

  const uniqUsersIdsByTags = useMemo<UUID[]>(() => {
    return Array.from(
      usersByTags.reduce((map, user) => map.set(user.id, user.id), new Map()).values(),
    )
  }, [usersByTags])

  const finalTargetUsers = useMemo(
    () =>
      targets?.all_users_ids?.filter(targetUserId => !uniqUsersIdsByTags.includes(targetUserId)),
    [uniqUsersIdsByTags, targets],
  )

  const onResetTree = useResetTreeCheck()

  const onSubmit: SubmitHandler<CreateCampaignForm> = async data => {
    const {
      name,
      isDelay,
      isTesting,
      text,
      theme,
      targetsAutoCourse,
      targetsMessage,
      targets,
      excludedTags,
      templates,
      period,
      redirect_page,
      email_send_end_date,
    } = data

    const body: any = {
      name: name,
      is_testing: isTesting,
      emails_templates: templates || [],
      email_send_end_date,
    }

    if (redirect_page && !enableRedirect) body.redirect_page = redirect_page

    if (campaign.targetType === 'all') {
      body.targets = {
        organizations: [userOrganizationId ?? ''],
        departments: [],
        employees: [],
      }
    }

    if (campaign.enableCreateCampaignByEnding) {
      const canRedirectPage = !enableRedirect && campaignFormByEnding?.redirect_page
      body.post_campaign = {
        targets: campaignForm?.post_campaign_targets,
        emails_templates: campaignFormByEnding?.templates,
        redirect_page: canRedirectPage ? campaignFormByEnding?.redirect_page : undefined,
        is_testing: campaignFormByEnding?.isTesting,
        enable_redirect: campaignFormByEnding?.enableRedirect,
        name: campaignFormByEnding?.name,

        exclude_users_by_tags_ids:
          campaignFormByEnding?.excludedTags && campaignFormByEnding?.excludedTags?.length > 0
            ? campaignFormByEnding?.excludedTags?.map(tag => tag?.id)
            : undefined,
        course: campaignFormByEnding?.hasAutoCourse
          ? {
              target: campaignFormByEnding?.targetsAutoCourse,
              course_id: campaignFormByEnding?.course,
              period: campaignFormByEnding?.period,
            }
          : undefined,
        notification: campaignFormByEnding?.hasMessage
          ? {
              targets: campaignFormByEnding?.targetsMessage,
              subject: campaignFormByEnding?.theme,
              text: campaignFormByEnding?.text,
            }
          : undefined,
        display_course_link: true,
      }
    }

    if (campaign.targetType === 'filtered') {
      body.targets = {
        organizations: [],
        departments: targets?.target_departments || [],
        employees: targets?.target_users || [],
        exclude_users_ids: targets?.exclude_users_ids ?? [],
      }
    }

    if (campaign.targetType === 'custom') {
      if (!((targets?.target_departments?.length || 0) + (targets?.target_users?.length || 0))) {
        return setError(t('select_campaign_target'))
      }

      body.targets = {
        organizations: [],
        departments: targets?.target_departments || [],
        employees: targets?.target_users || [],
        exclude_users_ids: targets?.exclude_users_ids ?? [],
      }
    }

    //TODO: change string to string[]
    if (excludedTags?.length) body.exclude_users_by_tags_ids = excludedTags.map(tag => tag.id)
    if (isDelay) body.start_date = dateDelay
    if (dateEnd && campaign.dateType === 'choose') body.end_date = dateEnd
    if (enableRedirect && !redirect_page) body.enable_redirect = enableRedirect
    if (hasMessage) {
      body.notification = {
        targets: targetsMessage,
        subject: theme,
        text: text,
      }
    }
    if (hasAutoCourse) {
      body.course = {
        targets: targetsAutoCourse,
        course_id: course,
        period: period,
      }
    }

    try {
      if (campaign.targetType !== 'filtered') {
        const data = await createPhishingCampaign(body).unwrap()
        onResetTree()
        dispatch(setInitialForms())
        setError('')
        navigate(`/lk/admin/phishing/campaigns/${data.id}`)
      }

      if (campaign.targetType === 'filtered') {
        const data = await createPhishingCampaignByFilteredUsers({
          params: {
            course_progress:
              filter?.courseProgress?.length > 0 ? filter?.courseProgress : undefined,
            in_course: filter?.courses?.length > 0 ? filter?.courses : undefined,
            in_phishing: filter?.phishing?.length > 0 ? filter?.phishing : undefined,
            phishing_events:
              filter?.phishingEvents?.length > 0 ? filter?.phishingEvents : undefined,
            risk_level_from: filter?.riskLevelMin,
            risk_level_to: filter?.riskLevelMax,
            roles: filter?.role || undefined,
            departments: filter?.departments?.length ? filter?.departments : undefined,
            learning: filter?.learning?.length ? filter?.learning : undefined,
            tags: filter?.tags?.length > 0 ? filter?.tags : undefined,
            search,
            need_all: emplIsAllSelected,
            exclude_ids: excludeEmployees ?? [],
            include_ids: checkedEmployees ?? [],
          },
          body: {
            ...body,
            targets: undefined,
            exclude_ids: excludeEmployees ?? [],
            include_ids: checkedEmployees ?? [],
          },
        }).unwrap()
        setError('')
        onResetTree()
        dispatch(setInitialForms())
        navigate(`/lk/admin/phishing/campaigns/${data.id}`)
      }
    } catch (e: any) {
      setError(handleErrorResponseAndTranslate(e))
    }
  }

  useEffect(() => {
    if (
      campaign.dateType === 'auto' &&
      finalTargetUsers?.length &&
      campaign.targetType === 'custom'
    ) {
      getAutoPhishFinishDate({
        targets: finalTargetUsers?.length,
        startDate: dateDelay ? new Date(dateDelay).toISOString() : undefined,
      })
      return
    }

    if (campaign.targetType === 'filtered' && campaign.dateType === 'auto') {
      getAutoPhishFinishDate({
        targets: getCountOfEmployess(),
        startDate: dateDelay ? new Date(dateDelay).toISOString() : undefined,
      })
    }
  }, [
    campaign.dateType,
    campaign.targetType,
    getCountOfEmployess,
    getAutoPhishFinishDate,
    finalTargetUsers,
    dateDelay,
  ])

  useEffect(() => {
    if (!targets) return

    if (
      targets.target_departments.length === 0 &&
      targets.target_users.length === 0 &&
      lastLoadedCountOfPeople
    ) {
      setNewCountOfEmployes(undefined)
    }
  }, [targets, lastLoadedCountOfPeople])

  const handleTrim = (str: string, name: keyof CreateCampaignForm, isRequired = false) => {
    if (!str.trim().length && isRequired) {
      setErrorForm(name, { type: 'custom', message: t('commons:required_field') })
    }
    setValue(name, str.trim())
  }

  const handleChangeEnableRedirect = () => {
    setValue('enableRedirect', !enableRedirect)
  }
  const handleChangeIsTesting = () => {
    setValue('isTesting', !isTesting)
  }
  const handleChangeIsDelay = () => {
    setValue('isDelay', !isDelay)
  }
  const handleChangeHasAutoCourse = () => {
    setValue('hasAutoCourse', !hasAutoCourse)
  }
  const handleChangeHasMessage = () => {
    setValue('hasMessage', !hasMessage)
  }
  const handleChangeTargetsAutoCourse = useCallback(
    (i: IListItem) => {
      if (!targetsAutoCourse) return

      const isIdAlreadyExists = targetsAutoCourse.includes(i.id as TTargetAutoCourseType)

      if (isIdAlreadyExists && targetsAutoCourse.length === 1) return

      if (isIdAlreadyExists) {
        setValue(
          'targetsAutoCourse',
          targetsAutoCourse?.filter(targetId => targetId !== i.id),
        )
      } else {
        setValue('targetsAutoCourse', [...targetsAutoCourse, i.id as TTargetAutoCourseType])
      }
    },
    [targetsAutoCourse],
  )

  const handleChangeTargetsMessage = useCallback(
    (i: IListItem) => {
      if (!targetsMessage) return

      const isIdAlreadyExists = targetsMessage.includes(i.id as TTargetAutoCourseType)

      if (isIdAlreadyExists && targetsMessage.length === 1) return

      if (isIdAlreadyExists) {
        setValue(
          'targetsMessage',
          targetsMessage?.filter(targetId => targetId !== i.id),
        )
      } else {
        setValue('targetsMessage', [...targetsMessage, i.id as TTargetAutoCourseType])
      }
    },
    [targetsMessage],
  )

  const handleChangeNumber = (value: string, name: keyof CreateCampaignForm) => {
    if (!isNaN(+value)) setValue(name, `${+value}`)
  }

  const handleAuthCourseSelect = (id?: UUID | null) => setValue('course', id)
  const handleChangeTemplate = (ids: UUID[]) => setValue('templates', ids)
  const handleChangeTargets = (target: ITargetData) => setValue('targets', target)
  const handleChangeDateEnd = (date: Date | null) => {
    setValue('dateEnd', date ? new Date(date) : null)
    const emailEndDate = watch('email_send_end_date')
    if (emailEndDate && date && emailEndDate > date) {
      setValue('email_send_end_date', date)
      add({
        message: t('end_date_msg'),
        status: 'error',
        withoutAdditionalInfo: true,
        id: uuid(),
      })
    }
  }
  const handleChangeDateDelay = (date: Date | null) => setValue('dateDelay', date)
  const handleChangeExcludedTags = (tags: ITagWithNew[]) => setValue('excludedTags', tags)
  const renderAutoCourseTargets = useCallback(() => {
    const renderedTargets = TARGET_AUTO_COURSE_LIST.filter(target =>
      targetsAutoCourse?.includes(target.id),
    )

    return renderedTargets.map(
      (target, index) => `${target.title}${index + 1 < renderedTargets.length ? ', ' : ''}`,
    )
  }, [TARGET_AUTO_COURSE_LIST, targetsAutoCourse])

  const renderMessageTargets = useCallback(() => {
    const renderedTargets = TARGET_AUTO_COURSE_LIST.filter(target =>
      targetsMessage?.includes(target.id),
    )

    return renderedTargets.map(
      (target, index) => `${target.title}${index + 1 < renderedTargets.length ? ', ' : ''}`,
    )
  }, [TARGET_AUTO_COURSE_LIST, targetsMessage])

  const isNotFinalTargets = () => {
    if (campaign.targetType === 'all') return false

    if (campaign.targetType === 'filtered' && getCountOfEmployess() <= 0) return true

    return false
  }
  const isDisabled =
    !isValid ||
    !templates?.length ||
    (campaign.targetType === 'custom' &&
      !((targets?.target_departments.length || 0) + (targets?.target_users.length || 0))) ||
    (hasAutoCourse && !course) ||
    (hasMessage && (!theme || !text)) ||
    isNotFinalTargets() ||
    (campaign.enableRedirectPage && !watch('redirect_page')) ||
    (campaign.enableCreateCampaignByEnding && !campaign.enableCreateCampaignByEndingSubmit)

  const autoGenerate = () => {
    const now = new Date()

    const day = String(now.getDate()).padStart(2, '0')
    const month = String(now.getMonth() + 1).padStart(2, '0') // Месяцы начинаются с 0
    const year = now.getFullYear()
    const hours = String(now.getHours()).padStart(2, '0')
    const minutes = String(now.getMinutes()).padStart(2, '0')

    const currentDate = `${day}.${month}.${year}`
    const currentTime = `${hours}:${minutes}`

    const value = `${t('phishing_campaign')} ${currentDate} ${currentTime}`

    setValue('name', value, { shouldValidate: true })
  }

  return (
    <div className={cx('wrapper', className)}>
      <div className={cx('inner')}>
        <Breadcrumbs items={breadcrumbItems} className={cx('breadcrumb', 'half')} />
        <PageTitle>{t('creating_new_campaign')}</PageTitle>
        <form onSubmit={handleSubmit(onSubmit)} className={cx('formWrapper')}>
          <div className={cx('inputWrapper', 'itemWrapper', 'half')}>
            <Input
              fullWidth
              name='name'
              label={t('campaign_name')}
              placeholder={t('input_new_campaign')}
              error={errors.name?.message}
              register={register('name', {
                required: t('commons:required_field'),
                onBlur: e => handleTrim(e.target.value, 'name', true),
              })}
            />
            {!name && (
              <button onClick={autoGenerate} className={cx('autoGenerate')}>
                {t('auto')}
                <HelpIcon text={t('phishing_campaign_hint')} />
              </button>
            )}
          </div>
          <div className={cx('itemWrapper')}>
            <TabSelector
              itemClassName={cx('target__item')}
              wrapperClass={cx('target')}
              label={t('campaign_target')}
              items={TARGET_TYPES}
              active={campaign.targetType}
              onChange={handleChangeTargetType}
            />
            {campaign.targetType === 'custom' && (
              <div className={cx('itemWrapperTop', 'half')}>
                <SelectTargetCard
                  text={
                    isNumber(displayedUsersCount)
                      ? t(`commons:employees_choosed`, {
                          count: displayedUsersCount,
                        })
                      : t('select_targets')
                  }
                  active={isNumber(displayedUsersCount)}
                  setOpen={setTargetModalOpen}
                  modalSlot={
                    targetModalOpen && (
                      <OrganizationTreeProvider
                        open={targetModalOpen}
                        setOpen={setTargetModalOpen}
                        handleSelect={state => {
                          handleChangeTargets({
                            target_departments: state?.department_ids,
                            target_users: !state?.selectAll ? state?.users_ids : [],
                            exclude_users_ids: state?.selectAll ? state?.users_ids : [],
                            all_users_ids: state?.users_ids || [],
                          })

                          setNewCountOfEmployes(state?.countOfPeople)
                        }}
                      >
                        <OrganizationTree />
                      </OrganizationTreeProvider>
                    )
                  }
                />
              </div>
            )}
            {campaign.targetType === 'filtered' && (
              <div className={cx('itemWrapperTop', 'half', 'primary')}>
                {t('commons:employees_choosed', {
                  count: getCountOfEmployess(),
                })}
              </div>
            )}
          </div>
          <div className={cx('itemWrapper', 'half')}>
            <CustomTagsSelect
              label={t('exclude_tag')}
              handleChangeTags={handleChangeExcludedTags}
              inputBaseClassName={cx('inputBase')}
            />
            {finalTargetUsers?.length === 0 && (
              <p className={cx('error-text', 'mt-10')}>{t('exclude_all_employees')}</p>
            )}
          </div>
          <div className={cx('switchWrapper', 'half')}>
            <div className={cx('switchInner')}>
              <span>{t('delayed_campaign')}</span>
              <Switch onChange={handleChangeIsDelay} customValue={isDelay} />
            </div>
            {isDelay && (
              <SelectDateCardWithInput
                min={new Date()}
                text={t('date_choose')}
                selected={dateDelay || null}
                onChange={handleChangeDateDelay}
              />
            )}
          </div>
          <div className={cx('itemWrapper', 'half')}>
            <TabSelector
              label={t('links_ttl')}
              help={t('links_ttl_help')}
              items={DATE_TYPES}
              active={campaign.dateType}
              onChange={handleChangeDateType}
            />
            {campaign.dateType === 'choose' && (
              <SelectDateCardWithInput
                text={t('date_choose')}
                selected={dateEnd || null}
                onChange={handleChangeDateEnd}
                min={new Date()}
              />
            )}
            {finishData?.campaign_end_date &&
              campaign.dateType === 'auto' &&
              (campaign.targetType === 'custom' || campaign.targetType === 'filtered') && (
                <div className={cx('ttl__info', finishDataIsLoading && 'ttl__info_loading')}>
                  {t('estimated_link_lifetime')}{' '}
                  <span className={cx('ttl__date')}>
                    {getPrettyDate(new Date(finishData?.campaign_end_date), dateLocale)}
                  </span>
                </div>
              )}
          </div>
          <div className={cx('itemWrapper', 'half')}>
            <TabSelector
              label={t('emails_period')}
              help={t('emails_period_help')}
              items={DATE_EMAIL_SEND_END_TYPES}
              active={campaign.emailSendType}
              onChange={i => {
                dispatch(setCreateCampaign({ emailSendType: i.name as TEmailSendEndType }))

                if (i?.name === 'to_end') {
                  setValue('email_send_end_date', undefined)
                }
              }}
            />
            {campaign.emailSendType === 'choose' && (
              <SelectDateCardWithInput
                text={t('end_date_choose')}
                selected={watch('email_send_end_date') || null}
                onChange={v => {
                  if (!v) {
                    setValue('email_send_end_date', v)
                    return
                  }
                  const dateEnd = watch('dateEnd')
                  const dateWithoutSec = new Date(v?.setSeconds(0))
                  const dateEndwithoutSec = dateEnd && new Date(dateEnd?.setSeconds(0))

                  if (v && dateEnd && dateEndwithoutSec && dateWithoutSec > dateEndwithoutSec) {
                    setValue('email_send_end_date', new Date(dateEndwithoutSec))
                    add({
                      message: t('end_date_msg'),
                      status: 'error',
                      withoutAdditionalInfo: true,
                      id: uuid(),
                    })
                  } else {
                    setValue('email_send_end_date', v)
                  }
                }}
                min={new Date()}
              />
            )}
          </div>
          <SelectTemplateCard
            label={t('commons:templates')}
            text={
              templates?.length == 0
                ? t('campaign_choose')
                : t('commons:templates_choosed', { count: templates?.length })
            }
            selected={templates || []}
            onChange={handleChangeTemplate}
            className={cx('itemWrapper', 'half')}
          />
          {!campaign.enableRedirectPage && (
            <div className={cx('switchWrapper', 'switchInner')}>
              <span>{t('redirect_to_orig_resource')}</span>
              <Switch onChange={handleChangeEnableRedirect} customValue={enableRedirect} />
              <HelpIcon text={t('redirect_to_orig_resource_help')} />
            </div>
          )}
          {!enableRedirect && (
            <>
              <div className={cx('switchWrapper', 'switchInner', 'half')}>
                <span>{t('redirect_to_chosen_page')}</span>
                <Switch
                  onChange={() => {
                    if (campaign.enableRedirectPage) setValue('redirect_page', undefined)

                    dispatch(
                      setCreateCampaign({ enableRedirectPage: !campaign?.enableRedirectPage }),
                    )
                  }}
                  customValue={campaign.enableRedirectPage}
                />
                <HelpIcon text={t('redirect_to_chosen_page_help')} />
              </div>
              {campaign.enableRedirectPage && (
                <div className={cx('selected__page__wrapper', 'half')}>
                  <SelectRedirectPage
                    onSelect={v => setValue('redirect_page', v)}
                    selected={watch('redirect_page') ?? undefined}
                  />
                </div>
              )}
            </>
          )}
          <div className={cx('switchWrapper', 'switchInner', 'half')}>
            <span>{t('testing_campaign')}</span>
            <Switch onChange={handleChangeIsTesting} customValue={isTesting} />
            <HelpIcon text={t('testing_campaign_help')} />
          </div>
          <div className={cx('switchWrapper', 'half')}>
            <div className={cx('switchInner')}>
              <span>{t('assign_course_after_campaign')} </span>
              <Switch onChange={handleChangeHasAutoCourse} customValue={hasAutoCourse} />
              <HelpIcon text={t('assign_course_after_campaign_help')} />
            </div>
            {hasAutoCourse && (
              <div className={cx('autoCourseInner', 'half')}>
                <SelectCourseCard
                  text={t('select_course')}
                  onChange={handleAuthCourseSelect}
                  className={cx('itemWrapper')}
                  selected={typeof course === 'string' ? [course] : []}
                />
                <MultiSelect
                  label={t('for_those_who')}
                  onChange={handleChangeTargetsAutoCourse}
                  customValue={targetsAutoCourse}
                  list={TARGET_AUTO_COURSE_LIST}
                  className={cx('multiselectWrap')}
                  inputBaseClassName={cx('multiselect')}
                  renderValue={renderAutoCourseTargets}
                />
                <Input
                  fullWidth
                  name='period'
                  label={t('select_course_period')}
                  placeholder={t('commons:enter_course_duration')}
                  classNameWrapper={cx('itemWrapper')}
                  error={errors.period?.message}
                  register={register('period', {
                    pattern: {
                      value: /^\d+$/,
                      message: t('commons:enter_number'),
                    },
                    onBlur: e => handleTrim(e.target.value, 'period', true),
                    onChange: e => handleChangeNumber(e.target.value, 'period'),
                  })}
                />
              </div>
            )}
          </div>
          <div className={cx('switchWrapper', 'half', 'new_campaign_by_ending')}>
            <div className={cx('switchInner')}>
              <span>{t('creating_new_campaign_by_ending')}</span>
              <Switch
                onChange={() => {
                  dispatch(
                    setCreateCampaign({
                      enableCreateCampaignByEnding: !campaign.enableCreateCampaignByEnding,
                    }),
                  )

                  dispatch(setPostCampaignForm({}))
                }}
                customValue={campaign.enableCreateCampaignByEnding}
              />
            </div>

            {campaign.enableCreateCampaignByEnding && (
              <>
                <MultiSelect
                  label={t('for_those_who')}
                  onChange={(i: IListItem) => {
                    const targets = campaignForm?.post_campaign_targets
                    if (!targets) return

                    const isIdAlreadyExists = targets.includes(i.id as TTargetAutoCourseType)

                    if (isIdAlreadyExists && targets.length === 1) return

                    if (isIdAlreadyExists) {
                      setValue(
                        'post_campaign_targets',
                        targets?.filter(targetId => targetId !== i.id),
                      )
                    } else {
                      setValue('post_campaign_targets', [...targets, i.id as TTargetAutoCourseType])
                    }
                  }}
                  customValue={watch('post_campaign_targets') ?? []}
                  list={TARGET_AUTO_COURSE_LIST}
                  className={cx('multiselectWrap__withoutMargin')}
                  inputBaseClassName={cx('multiselect')}
                  renderValue={() => {
                    const targets = watch('post_campaign_targets') ?? []
                    const renderedTargets = TARGET_AUTO_COURSE_LIST.filter(target =>
                      targets?.includes(target.id),
                    )

                    return renderedTargets.map(
                      (target, index) =>
                        `${target.title}${index + 1 < renderedTargets.length ? ', ' : ''}`,
                    )
                  }}
                />
                <Button
                  type='button'
                  className={cx('primary-text')}
                  withoutBackground={true}
                  onClick={() => navigate(URLS.ADMIN_PHISHING_CAMPAIGNS_BY_ENDING_CREATE_PAGE)}
                  fullWidth
                >
                  {t('goto_creating_new_campaign_by_ending')}
                </Button>
              </>
            )}
          </div>
          <div className={cx('switchWrapper', 'half')}>
            <div className={cx('switchInner')}>
              <span>{t('send_msg_after_campaign')}</span>
              <Switch onChange={handleChangeHasMessage} customValue={hasMessage} />
              <HelpIcon text={t('send_msg_after_campaign_help')} />
            </div>
            {hasMessage && (
              <div className={cx('autoCourseInner', 'half')}>
                <MultiSelect
                  label={t('for_those_who')}
                  onChange={handleChangeTargetsMessage}
                  customValue={targetsMessage}
                  list={TARGET_AUTO_COURSE_LIST}
                  className={cx('multiselectWrap')}
                  inputBaseClassName={cx('multiselect')}
                  renderValue={renderMessageTargets}
                />
                <Input
                  fullWidth
                  name='theme'
                  label={t('commons:mail_theme')}
                  placeholder={t('commons:mail_theme')}
                  classNameWrapper={cx('itemWrapper')}
                  error={errors.theme?.message}
                  register={register('theme', {
                    onBlur: e => handleTrim(e.target.value, 'theme', true),
                  })}
                />
                <Textarea
                  fullWidth
                  name='text'
                  label={t('email_text')}
                  placeholder={t('send_msg_after_campaign_text_placeholder')}
                  classNameWrapper={cx('itemWrapper')}
                  error={errors.text?.message}
                  register={register('text', {
                    onBlur: e => handleTrim(e.target.value, 'text', true),
                  })}
                />
              </div>
            )}
          </div>
          {error && <div className={cx('error-text', 'errorText')}>{error}</div>}
          {finalTargetUsers?.length === 0 && (
            <div className={cx('error-text', 'errorText')}>{t('exclude_all_employees')}</div>
          )}
          <Button
            type='submit'
            fullWidth
            className={cx('itemWrapper', 'half')}
            disabled={isDisabled}
            loading={isLoading}
          >
            {t('submit')}
          </Button>
          <div className={cx('tip', 'half')}>{t('info')}</div>
        </form>
      </div>
    </div>
  )
}
